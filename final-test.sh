#!/bin/bash

echo "🎯 OnePaste 快捷键修复最终测试"
echo "================================"
echo ""

# 检查应用是否在运行
if ! pgrep -f "OnePaste" > /dev/null; then
    echo "❌ OnePaste 应用未运行"
    echo "请先启动应用，然后重新运行此脚本"
    exit 1
fi

echo "✅ OnePaste 应用正在运行"
echo ""

echo "🔧 修复内容总结："
echo "1. 简化了快捷键注册逻辑，避免复杂的错误处理"
echo "2. 移除了窗口切换状态的复杂事件通知机制"
echo "3. 简化了窗口焦点处理，减少竞态条件"
echo "4. 优化了窗口显示/隐藏的判断逻辑"
echo ""

echo "📋 测试步骤："
echo "1. 应用已启动并在后台运行"
echo "2. 请按下 Alt+C (Option+C) 快捷键"
echo "3. 观察应用是否正常显示剪贴板界面"
echo "4. 再次按下 Alt+C 应该隐藏窗口"
echo "5. 检查应用是否保持稳定运行"
echo ""

echo "🔍 开始监控应用状态 (30秒)..."
echo "请在此期间测试快捷键功能"
echo ""

# 监控应用状态
for i in {1..30}; do
    if pgrep -f "OnePaste" > /dev/null; then
        echo -n "✅ "
        if [ $((i % 10)) -eq 0 ]; then
            echo " (${i}s)"
        fi
    else
        echo ""
        echo "❌ 应用已退出！"
        
        # 检查是否有新的崩溃日志
        LATEST_CRASH=$(ls -t ~/Library/Logs/DiagnosticReports/OnePaste-*.ips 2>/dev/null | head -1)
        if [ -n "$LATEST_CRASH" ]; then
            CRASH_TIME=$(stat -f "%Sm" -t "%Y-%m-%d %H:%M:%S" "$LATEST_CRASH")
            if [[ $(date -j -f "%Y-%m-%d %H:%M:%S" "$CRASH_TIME" "+%s") -gt $(($(date "+%s") - 60)) ]]; then
                echo "⚠️  检测到新崩溃日志: $LATEST_CRASH"
                echo "崩溃时间: $CRASH_TIME"
            fi
        fi
        
        exit 1
    fi
    sleep 1
done

echo ""
echo ""
echo "🎉 测试完成！应用运行稳定"
echo ""

echo "✨ 如果快捷键正常工作且应用没有崩溃，说明问题已成功修复！"
echo ""
echo "📝 修复要点："
echo "- 使用直接的全局快捷键注册，避免复杂的回退机制"
echo "- 简化窗口状态管理，减少事件通信"
echo "- 优化焦点处理逻辑，避免竞态条件"
echo "- 改进错误处理，提高稳定性"
