#!/bin/bash

echo "🧪 OnePaste 快捷键修复测试"
echo "=========================="
echo ""

# 检查应用是否在运行
if ! pgrep -f "OnePaste" > /dev/null; then
    echo "❌ OnePaste 应用未运行"
    echo "请先启动应用，然后重新运行此脚本"
    exit 1
fi

echo "✅ OnePaste 应用正在运行"
echo ""

# 检查最新的崩溃日志
echo "🔍 检查崩溃日志..."
LATEST_CRASH=$(ls -t ~/Library/Logs/DiagnosticReports/OnePaste-*.ips 2>/dev/null | head -1)

if [ -n "$LATEST_CRASH" ]; then
    CRASH_TIME=$(stat -f "%Sm" -t "%Y-%m-%d %H:%M:%S" "$LATEST_CRASH")
    echo "最新崩溃日志: $LATEST_CRASH"
    echo "崩溃时间: $CRASH_TIME"
    
    # 检查是否是最近5分钟内的崩溃
    if [[ $(date -j -f "%Y-%m-%d %H:%M:%S" "$CRASH_TIME" "+%s") -gt $(($(date "+%s") - 300)) ]]; then
        echo "⚠️  检测到最近的崩溃"
        echo "请查看崩溃详情:"
        echo "tail -20 '$LATEST_CRASH'"
    else
        echo "✅ 没有检测到最近的崩溃"
    fi
else
    echo "✅ 没有找到崩溃日志"
fi

echo ""
echo "🎯 测试说明："
echo "1. 应用已启动并正在运行"
echo "2. 现在请按下 Alt+C (Option+C) 快捷键"
echo "3. 观察应用是否正常显示剪贴板界面"
echo "4. 如果应用没有崩溃，说明修复成功！"
echo ""
echo "📋 修复内容："
echo "- 添加了多层错误保护机制"
echo "- 实现了全局快捷键失败时的回退方案"
echo "- 增加了错误边界防止应用崩溃"
echo "- 优化了权限检查和沙盒兼容性"
echo ""
echo "✨ 如果快捷键正常工作且应用不崩溃，说明问题已解决！"

# 监控应用状态
echo ""
echo "🔄 监控应用状态 (10秒)..."
for i in {1..10}; do
    if pgrep -f "OnePaste" > /dev/null; then
        echo -n "✅ "
    else
        echo ""
        echo "❌ 应用已退出，可能发生了崩溃"
        exit 1
    fi
    sleep 1
done

echo ""
echo "🎉 应用运行稳定，修复成功！"
