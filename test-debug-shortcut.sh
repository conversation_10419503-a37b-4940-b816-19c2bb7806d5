#!/bin/bash

echo "🧪 OnePaste 快捷键调试测试"
echo "=========================="
echo ""

# 检查应用是否在运行
if ! pgrep -f "OnePaste" > /dev/null; then
    echo "❌ OnePaste 应用未运行"
    echo "请先启动应用，然后重新运行此脚本"
    exit 1
fi

echo "✅ OnePaste 应用正在运行"
echo ""

echo "📋 测试说明："
echo "1. 应用已启动并在后台运行"
echo "2. 现在请按下 Alt+C (Option+C) 快捷键"
echo "3. 观察控制台输出的调试信息"
echo "4. 检查应用是否正常显示剪贴板界面"
echo ""

echo "🔍 监控应用状态和日志..."
echo "按 Ctrl+C 停止监控"
echo ""

# 监控应用状态
while true; do
    if pgrep -f "OnePaste" > /dev/null; then
        echo -n "✅ $(date '+%H:%M:%S') - 应用运行中 "
        
        # 检查是否有新的崩溃日志
        LATEST_CRASH=$(ls -t ~/Library/Logs/DiagnosticReports/OnePaste-*.ips 2>/dev/null | head -1)
        if [ -n "$LATEST_CRASH" ]; then
            CRASH_TIME=$(stat -f "%Sm" -t "%Y-%m-%d %H:%M:%S" "$LATEST_CRASH")
            if [[ $(date -j -f "%Y-%m-%d %H:%M:%S" "$CRASH_TIME" "+%s") -gt $(($(date "+%s") - 10)) ]]; then
                echo "⚠️  检测到新崩溃！"
                break
            fi
        fi
        
        echo ""
    else
        echo "❌ $(date '+%H:%M:%S') - 应用已退出"
        break
    fi
    sleep 2
done

echo ""
echo "🔍 检查最新的系统日志..."
log show --predicate 'process == "OnePaste"' --last 1m --style compact | tail -10

echo ""
echo "✨ 测试完成！"
echo "如果看到调试信息输出，说明快捷键处理逻辑正在工作"
