#!/bin/bash

# OnePaste App Store 构建脚本

set -e

echo "🚀 开始构建 OnePaste App Store 版本..."

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 检查必要的工具
check_requirements() {
    echo "📋 检查构建环境..."
    
    # 检查 Rust
    if ! command -v cargo &> /dev/null; then
        echo -e "${RED}❌ 未安装 Rust${NC}"
        exit 1
    fi
    
    # 检查 Node.js
    if ! command -v node &> /dev/null; then
        echo -e "${RED}❌ 未安装 Node.js${NC}"
        exit 1
    fi
    
    # 检查 pnpm
    if ! command -v pnpm &> /dev/null; then
        echo -e "${RED}❌ 未安装 pnpm${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ 环境检查通过${NC}"
}

# 清理之前的构建
clean_build() {
    echo "🧹 清理之前的构建..."
    rm -rf dist
    rm -rf src-tauri/target/release/bundle
    echo -e "${GREEN}✅ 清理完成${NC}"
}

# 构建前端
build_frontend() {
    echo "🎨 构建前端 (App Store 版本)..."

    # 设置 App Store 环境变量
    export VITE_APP_STORE=true

    pnpm build
    echo -e "${GREEN}✅ 前端构建完成${NC}"
}

# 构建 Tauri 应用（App Store 版本）
build_tauri() {
    echo "🔨 构建 Tauri 应用 (App Store 版本)..."

    # 备份原始 Cargo.toml
    cp src-tauri/Cargo.toml src-tauri/Cargo.toml.backup

    # 使用 App Store 专用配置
    cp src-tauri/Cargo.app-store.toml src-tauri/Cargo.toml

    # 构建应用
    pnpm tauri build --features app-store

    # 恢复原始配置
    mv src-tauri/Cargo.toml.backup src-tauri/Cargo.toml

    echo -e "${GREEN}✅ Tauri 构建完成${NC}"
}

# 签名应用
sign_app() {
    echo "✍️ 签名应用..."
    
    APP_PATH="src-tauri/target/release/bundle/macos/OnePaste.app"
    
    if [ ! -d "$APP_PATH" ]; then
        echo -e "${RED}❌ 找不到应用包: $APP_PATH${NC}"
        exit 1
    fi
    
    # 使用正确的签名标识
    SIGNING_IDENTITY="3rd Party Mac Developer Application: Technetium LLC (2RMYG9B3XY)"
    
    # 签名应用
    codesign --deep --force --verify --verbose \
        --sign "$SIGNING_IDENTITY" \
        --options runtime \
        --entitlements src-tauri/entitlements.plist \
        "$APP_PATH"
    
    # 验证签名
    codesign --verify --verbose "$APP_PATH"
    
    echo -e "${GREEN}✅ 签名完成${NC}"
}

# 创建安装包
create_installer() {
    echo "📦 创建安装包..."
    
    APP_PATH="src-tauri/target/release/bundle/macos/OnePaste.app"
    PKG_PATH="src-tauri/target/release/bundle/macos/OnePaste.pkg"
    
    # 使用安装器签名标识
    INSTALLER_IDENTITY="3rd Party Mac Developer Installer: Technetium LLC (2RMYG9B3XY)"
    
    # 创建 pkg 安装包
    productbuild --component "$APP_PATH" /Applications \
        --sign "$INSTALLER_IDENTITY" \
        "$PKG_PATH"
    
    echo -e "${GREEN}✅ 安装包创建完成: $PKG_PATH${NC}"
}

# 验证应用
verify_app() {
    echo "🔍 验证应用..."
    
    APP_PATH="src-tauri/target/release/bundle/macos/OnePaste.app"
    
    # 检查签名
    echo "检查签名..."
    codesign -dvv "$APP_PATH"
    
    # 检查 entitlements
    echo "检查 entitlements..."
    codesign -d --entitlements - "$APP_PATH"
    
    # 使用 spctl 检查
    echo "检查 Gatekeeper..."
    spctl -a -vvv "$APP_PATH" || echo -e "${YELLOW}⚠️ Gatekeeper 检查失败（这在开发环境中是正常的）${NC}"
    
    echo -e "${GREEN}✅ 验证完成${NC}"
}

# 主函数
main() {
    echo "================================================"
    echo "   OnePaste App Store 构建脚本"
    echo "================================================"
    echo ""
    
    check_requirements
    clean_build
    build_frontend
    build_tauri
    sign_app
    create_installer
    verify_app
    
    echo ""
    echo "================================================"
    echo -e "${GREEN}🎉 构建成功！${NC}"
    echo ""
    echo "生成的文件："
    echo "  - 应用包: src-tauri/target/release/bundle/macos/OnePaste.app"
    echo "  - 安装包: src-tauri/target/release/bundle/macos/OnePaste.pkg"
    echo ""
    echo "下一步："
    echo "  1. 使用 Transporter 上传 .pkg 文件到 App Store Connect"
    echo "  2. 在 App Store Connect 中提交审核"
    echo "================================================"
}

# 运行主函数
main
