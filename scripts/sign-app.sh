#!/bin/bash

# OnePaste App Store 签名脚本
set -e

echo "🔐 开始 OnePaste 应用签名流程..."

# 配置变量
APP_NAME="OnePaste"
BUNDLE_ID="com.tcnum.onepaste"
APP_PATH="target/release/bundle/macos/${APP_NAME}.app"
SIGNED_APP_PATH="target/release/bundle/macos/${APP_NAME}-signed.app"
PKG_PATH="target/release/bundle/macos/${APP_NAME}.pkg"

# 证书名称（需要根据你的实际证书名称调整）
APP_CERT="3rd Party Mac Developer Application"
INSTALLER_CERT="3rd Party Mac Developer Installer"

# 检查应用是否存在
if [ ! -d "$APP_PATH" ]; then
    echo "❌ 错误：找不到应用文件 $APP_PATH"
    echo "请先运行构建命令：npm run build"
    exit 1
fi

echo "📦 找到应用：$APP_PATH"

# 复制应用以避免修改原文件
echo "📋 复制应用文件..."
cp -R "$APP_PATH" "$SIGNED_APP_PATH"

# 查找并显示可用的签名身份
echo "🔍 查找可用的签名证书..."
security find-identity -v -p codesigning

# 签名应用内的所有二进制文件
echo "✍️  签名应用内的二进制文件..."
find "$SIGNED_APP_PATH" -name "*.dylib" -exec codesign --force --verify --verbose --sign "$APP_CERT" {} \;
find "$SIGNED_APP_PATH" -name "*.so" -exec codesign --force --verify --verbose --sign "$APP_CERT" {} \;

# 签名主应用
echo "✍️  签名主应用..."
codesign --force --options runtime --entitlements src-tauri/entitlements.plist --sign "$APP_CERT" --verbose "$SIGNED_APP_PATH"

# 验证签名
echo "🔍 验证应用签名..."
codesign --verify --deep --strict --verbose=2 "$SIGNED_APP_PATH"
spctl --assess -vv --type install "$SIGNED_APP_PATH"

# 创建安装包
echo "📦 创建安装包..."
productbuild --component "$SIGNED_APP_PATH" /Applications --sign "$INSTALLER_CERT" "$PKG_PATH"

# 验证安装包
echo "🔍 验证安装包..."
pkgutil --check-signature "$PKG_PATH"

echo "✅ 签名完成！"
echo "📱 已签名的应用：$SIGNED_APP_PATH"
echo "📦 安装包：$PKG_PATH"
echo ""
echo "🚀 下一步：使用 Transporter 或 Xcode 上传 $PKG_PATH 到 App Store Connect"
