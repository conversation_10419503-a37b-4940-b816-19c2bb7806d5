#!/bin/bash

# OnePaste 快捷键测试脚本

echo "🧪 OnePaste 快捷键测试"
echo "======================"
echo ""

# 检查应用是否在运行
if ! pgrep -f "OnePaste" > /dev/null; then
    echo "❌ OnePaste 应用未运行"
    echo "请先启动应用，然后重新运行此脚本"
    exit 1
fi

echo "✅ OnePaste 应用正在运行"
echo ""

echo "📋 测试说明："
echo "1. 确保 OnePaste 应用已经启动"
echo "2. 尝试按下 Alt+C (Option+C) 快捷键"
echo "3. 观察应用是否正常显示剪贴板界面"
echo "4. 如果应用崩溃，请检查崩溃日志"
echo ""

echo "🔍 检查崩溃日志..."
CRASH_LOG=$(ls -t ~/Library/Logs/DiagnosticReports/OnePaste-*.ips 2>/dev/null | head -1)

if [ -n "$CRASH_LOG" ]; then
    CRASH_TIME=$(stat -f "%Sm" -t "%Y-%m-%d %H:%M:%S" "$CRASH_LOG")
    CURRENT_TIME=$(date "+%Y-%m-%d %H:%M:%S")
    
    echo "最新崩溃日志: $CRASH_LOG"
    echo "崩溃时间: $CRASH_TIME"
    echo "当前时间: $CURRENT_TIME"
    
    # 检查是否是最近5分钟内的崩溃
    if [[ $(date -j -f "%Y-%m-%d %H:%M:%S" "$CRASH_TIME" "+%s") -gt $(($(date "+%s") - 300)) ]]; then
        echo "⚠️  检测到最近的崩溃，可能与快捷键相关"
    else
        echo "✅ 没有检测到最近的崩溃"
    fi
else
    echo "✅ 没有找到崩溃日志"
fi

echo ""
echo "🎯 手动测试步骤："
echo "1. 按下 Alt+C (Option+C) 快捷键"
echo "2. 检查应用是否正常响应"
echo "3. 如果应用崩溃，请运行以下命令查看详细日志："
echo "   tail -50 ~/Library/Logs/DiagnosticReports/OnePaste-*.ips"
echo ""
echo "✨ 如果快捷键正常工作，说明修复成功！"
