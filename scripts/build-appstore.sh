#!/bin/bash

# App Store 构建脚本
set -e

echo "🚀 开始构建 OnePaste App Store 版本..."

# 清理之前的构建
echo "🧹 清理之前的构建..."
rm -rf src-tauri/target/release/bundle

# 设置环境变量
export TAURI_PRIVATE_KEY=""  # App Store 不需要更新密钥
export TAURI_KEY_PASSWORD=""

# 构建应用
echo "🔨 构建应用..."
npm run build

# 分别构建 Intel 和 Apple Silicon 版本
echo "🔨 构建 Intel 版本..."
cargo tauri build --target x86_64-apple-darwin

echo "🔨 构建 Apple Silicon 版本..."
cargo tauri build --target aarch64-apple-darwin

# 创建通用二进制
echo "🔨 创建通用二进制..."
mkdir -p target/universal-apple-darwin/release/bundle/macos
cp -R target/aarch64-apple-darwin/release/bundle/macos/OnePaste.app target/universal-apple-darwin/release/bundle/macos/

# 使用 lipo 合并二进制文件
lipo -create \
  target/x86_64-apple-darwin/release/bundle/macos/OnePaste.app/Contents/MacOS/OnePaste \
  target/aarch64-apple-darwin/release/bundle/macos/OnePaste.app/Contents/MacOS/OnePaste \
  -output target/universal-apple-darwin/release/bundle/macos/OnePaste.app/Contents/MacOS/OnePaste

# 查找生成的 .app 文件
APP_PATH="src-tauri/target/universal-apple-darwin/release/bundle/macos/OnePaste.app"

if [ ! -d "$APP_PATH" ]; then
    echo "❌ 构建失败：找不到 .app 文件"
    exit 1
fi

echo "✅ 构建完成！"
echo "📦 应用位置: $APP_PATH"

# 验证应用
echo "🔍 验证应用..."
codesign --verify --verbose=2 "$APP_PATH"

echo "🎉 构建完成！现在可以上传到 App Store Connect"
