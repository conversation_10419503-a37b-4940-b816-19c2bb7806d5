<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <!-- App Store 必需的权限 -->
    <key>com.apple.security.app-sandbox</key>
    <true/>

    <!-- 网络访问权限（用于更新检查） -->
    <key>com.apple.security.network.client</key>
    <true/>

    <!-- 文件系统访问权限 -->
    <key>com.apple.security.files.user-selected.read-write</key>
    <true/>

    <!-- 用户数据目录访问 -->
    <key>com.apple.security.files.downloads.read-write</key>
    <true/>

    <!-- 剪贴板访问权限 -->
    <key>com.apple.security.automation.apple-events</key>
    <true/>

    <!-- 辅助功能权限（用于全局快捷键和粘贴功能） -->
    <key>com.apple.security.temporary-exception.apple-events</key>
    <array>
        <string>com.apple.systemevents</string>
        <string>com.apple.finder</string>
    </array>

    <!-- 允许访问系统事件（用于快捷键） -->
    <key>com.apple.security.temporary-exception.shared-preference.read-write</key>
    <array>
        <string>com.apple.HIToolbox</string>
    </array>

    <!-- 如果需要访问其他应用的数据 -->
    <key>com.apple.security.temporary-exception.files.absolute-path.read-write</key>
    <array>
        <string>/tmp</string>
        <string>/var/folders</string>
    </array>

    <!-- 允许访问用户偏好设置 -->
    <key>com.apple.security.files.user-selected.read-only</key>
    <true/>

    <!-- 允许访问应用程序目录 -->
    <key>com.apple.security.files.downloads.read-only</key>
    <true/>
</dict>
</plist>
