import { useEffect, useState } from "react";
import { WebviewWindow } from "@tauri-apps/api/webviewWindow";
import { register, unregister } from "@tauri-apps/plugin-global-shortcut";

/**
 * App Store 兼容的快捷键注册 Hook
 * 处理沙盒环境下的快捷键注册问题
 */
export const useAppStoreShortcut = (
	shortcut: string | undefined,
	handler: () => void,
) => {
	const [isRegistered, setIsRegistered] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const [useAlternative, setUseAlternative] = useState(false);

	// 全局快捷键注册
	useEffect(() => {
		if (!shortcut || useAlternative) return;

		let mounted = true;

		const registerShortcut = async () => {
			try {
				// 先尝试注销之前的快捷键
				try {
					await unregister(shortcut);
				} catch {
					// 忽略注销错误
				}

				// 尝试注册新的快捷键
				await register(shortcut, (event) => {
					if (event.state === "Released") return;
					if (mounted) {
						handler();
					}
				});

				if (mounted) {
					setIsRegistered(true);
					setError(null);
				}
			} catch (err) {
				console.warn(`快捷键注册失败 (${shortcut}):`, err);
				if (mounted) {
					setIsRegistered(false);
					setError(err instanceof Error ? err.message : String(err));

					// 对于 App Store 版本，使用备用方案
					setUseAlternative(true);
				}
			}
		};

		registerShortcut();

		return () => {
			mounted = false;
			if (isRegistered) {
				unregister(shortcut).catch(console.warn);
			}
		};
	}, [shortcut, useAlternative, handler]);

	// 备用方案：应用内快捷键监听
	useAppStoreAlternative(shortcut, handler, useAlternative);

	return { isRegistered, error };
};

/**
 * App Store 备用方案：使用应用内快捷键
 */
const useAppStoreAlternative = (shortcut: string | undefined, handler: () => void, enabled: boolean) => {
	// 为 App Store 版本提供备用方案
	// 例如：使用应用内快捷键监听或菜单项
	useEffect(() => {
		if (!shortcut || !enabled) return;
		const handleKeyDown = (event: KeyboardEvent) => {
			// 解析快捷键
			const parts = shortcut.toLowerCase().split("+");
			const hasAlt = parts.includes("alt") || parts.includes("option");
			const hasCmd = parts.includes("cmd") || parts.includes("command");
			const hasCtrl = parts.includes("ctrl") || parts.includes("control");
			const hasShift = parts.includes("shift");
			const key = parts[parts.length - 1];

			// 检查按键组合
			if (
				event.altKey === hasAlt &&
				event.metaKey === hasCmd &&
				event.ctrlKey === hasCtrl &&
				event.shiftKey === hasShift &&
				event.key.toLowerCase() === key
			) {
				event.preventDefault();
				handler();
			}
		};

		// 只在应用窗口获得焦点时监听
		const appWindow = WebviewWindow.getCurrent();
		let isWindowFocused = false;

		const unlistenFocus = appWindow.listen("tauri://focus", () => {
			isWindowFocused = true;
		});

		const unlistenBlur = appWindow.listen("tauri://blur", () => {
			isWindowFocused = false;
		});

		const wrappedHandler = (event: KeyboardEvent) => {
			if (isWindowFocused) {
				handleKeyDown(event);
			}
		};

		document.addEventListener("keydown", wrappedHandler);

		return () => {
			document.removeEventListener("keydown", wrappedHandler);
			unlistenFocus.then((fn) => fn());
			unlistenBlur.then((fn) => fn());
		};
	}, [shortcut, handler, enabled]);
};
