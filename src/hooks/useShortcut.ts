import { useRegister } from "./useRegister";
import { useAppStoreShortcut } from "./useAppStoreShortcut";
import type { ShortcutHandler } from "@tauri-apps/plugin-global-shortcut";
import { useState, useEffect } from "react";

/**
 * 统一的快捷键处理 Hook
 * 根据构建环境自动选择合适的实现，并提供回退机制
 */
export const useShortcut = (
	handler: ShortcutHandler | (() => void),
	deps: Array<string | string[] | undefined>,
) => {
	const [fallbackMode, setFallbackMode] = useState(false);
	const [error, setError] = useState<string | null>(null);

	// 检查是否为 App Store 版本或需要使用回退模式
	const isAppStore = import.meta.env.VITE_APP_STORE === "true";
	const shouldUseFallback = isAppStore || fallbackMode;

	// 尝试使用全局快捷键，如果失败则回退到应用内快捷键
	const globalShortcutResult = useRegister(handler as ShortcutHandler, shouldUseFallback ? [] : deps);

	// 监听全局快捷键注册错误，如果失败则启用回退模式
	useEffect(() => {
		if (globalShortcutResult?.registrationError && !shouldUseFallback) {
			console.warn("全局快捷键注册失败，启用回退模式:", globalShortcutResult.registrationError);
			setFallbackMode(true);
			setError(globalShortcutResult.registrationError);
		}
	}, [globalShortcutResult?.registrationError, shouldUseFallback]);

	// 回退模式：使用应用内快捷键
	const [shortcut] = deps;
	const wrappedHandler = typeof handler === 'function'
		? handler as () => void
		: () => {
			try {
				handler({ shortcut: shortcut as string, state: "Pressed" });
			} catch (err) {
				console.error("快捷键处理函数出错:", err);
			}
		};

	const fallbackResult = useAppStoreShortcut(
		shouldUseFallback ? shortcut as string : undefined,
		wrappedHandler
	);

	// 返回适当的结果
	if (shouldUseFallback) {
		return {
			...fallbackResult,
			fallbackMode: true,
			originalError: error,
		};
	} else {
		return {
			...globalShortcutResult,
			fallbackMode: false,
		};
	}
};
