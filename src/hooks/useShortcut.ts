import { useRegister } from "./useRegister";
import { useAppStoreShortcut } from "./useAppStoreShortcut";
import type { ShortcutHandler } from "@tauri-apps/plugin-global-shortcut";

/**
 * 统一的快捷键处理 Hook
 * 根据构建环境自动选择合适的实现
 */
export const useShortcut = (
	handler: ShortcutHandler | (() => void),
	deps: Array<string | string[] | undefined>,
) => {
	// 检查是否为 App Store 版本
	const isAppStore = import.meta.env.VITE_APP_STORE === "true";
	
	if (isAppStore) {
		// App Store 版本使用兼容的快捷键处理
		const [shortcut] = deps;
		const wrappedHandler = typeof handler === 'function' 
			? handler as () => void
			: () => handler({ shortcut: shortcut as string, state: "Pressed" });
		
		return useAppStoreShortcut(shortcut as string, wrappedHandler);
	} else {
		// 普通版本使用全局快捷键
		return useRegister(handler as ShortcutHandler, deps);
	}
};
