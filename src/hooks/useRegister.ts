import {
	type ShortcutHandler,
	isRegistered,
	register,
	unregister,
} from "@tauri-apps/plugin-global-shortcut";
import { castArray } from "lodash-es";

export const useRegister = (
	handler: ShortcutHandler,
	deps: Array<string | string[] | undefined>,
) => {
	const [oldShortcuts, setOldShortcuts] = useState(deps[0]);
	const [registrationError, setRegistrationError] = useState<string | null>(null);

	useAsyncEffect(async () => {
		const [shortcuts] = deps;

		// 添加安全检查，防止在不支持的环境下崩溃
		try {
			// 检查是否在支持的环境中
			if (typeof window === 'undefined') {
				console.warn("快捷键注册跳过：不在浏览器环境中");
				return;
			}

			// 先注销旧的快捷键
			for await (const shortcut of castArray(oldShortcuts)) {
				if (!shortcut) continue;

				try {
					const registered = await isRegistered(shortcut);
					if (registered) {
						await unregister(shortcut);
					}
				} catch (error) {
					console.warn(`无法注销快捷键 ${shortcut}:`, error);
				}
			}

			if (!shortcuts) return;

			// 尝试注册新的快捷键，添加超时保护
			try {
				const registrationPromise = register(shortcuts, (event) => {
					try {
						if (event.state === "Released") return;
						handler(event);
					} catch (handlerError) {
						console.error("快捷键处理函数出错:", handlerError);
					}
				});

				// 添加超时保护，防止注册过程卡死
				const timeoutPromise = new Promise((_, reject) => {
					setTimeout(() => reject(new Error("快捷键注册超时")), 5000);
				});

				await Promise.race([registrationPromise, timeoutPromise]);

				setOldShortcuts(shortcuts);
				setRegistrationError(null);
				console.log(`快捷键注册成功: ${shortcuts}`);
			} catch (error) {
				const errorMsg = error instanceof Error ? error.message : String(error);
				console.error(`快捷键注册失败 (${shortcuts}):`, errorMsg);
				setRegistrationError(errorMsg);

				// 如果是权限问题，提示用户
				if (errorMsg.includes("accessibility") || errorMsg.includes("permission")) {
					console.warn("需要辅助功能权限才能使用全局快捷键");
				}

				// 不要让错误传播，避免崩溃
				return;
			}
		} catch (error) {
			console.error("快捷键处理出错:", error);
			setRegistrationError(error instanceof Error ? error.message : String(error));
		}
	}, deps);

	useUnmount(() => {
		const [shortcuts] = deps;
		if (!shortcuts) return;

		try {
			unregister(shortcuts).catch(console.warn);
		} catch (error) {
			console.warn("注销快捷键失败:", error);
		}
	});

	return { registrationError };
};
