import { useState, useEffect, useCallback } from "react";

/**
 * 安全的快捷键处理 Hook
 * 提供多层错误保护，防止应用崩溃
 */
export const useSafeShortcut = (
	shortcut: string | undefined,
	handler: () => void,
) => {
	const [isActive, setIsActive] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const [method, setMethod] = useState<'global' | 'fallback' | 'disabled'>('global');

	// 安全的处理函数包装器
	const safeHandler = useCallback(() => {
		try {
			handler();
		} catch (err) {
			console.error("快捷键处理函数执行出错:", err);
			setError(err instanceof Error ? err.message : String(err));
		}
	}, [handler]);

	// 尝试注册全局快捷键
	const tryGlobalShortcut = useCallback(async () => {
		if (!shortcut) return false;

		try {
			// 动态导入，避免在不支持的环境中加载
			const { register, unregister, isRegistered } = await import("@tauri-apps/plugin-global-shortcut");
			
			// 先检查是否已经注册
			const alreadyRegistered = await isRegistered(shortcut);
			if (alreadyRegistered) {
				await unregister(shortcut);
			}

			// 注册新的快捷键
			await register(shortcut, (event) => {
				if (event.state === "Released") return;
				safeHandler();
			});

			setMethod('global');
			setIsActive(true);
			setError(null);
			console.log(`全局快捷键注册成功: ${shortcut}`);
			return true;
		} catch (err) {
			console.warn(`全局快捷键注册失败: ${shortcut}`, err);
			setError(err instanceof Error ? err.message : String(err));
			return false;
		}
	}, [shortcut, safeHandler]);

	// 回退到应用内快捷键监听
	const useFallbackShortcut = useCallback(() => {
		if (!shortcut) return;

		const handleKeyDown = (event: KeyboardEvent) => {
			// 解析快捷键
			const parts = shortcut.toLowerCase().split("+");
			const hasAlt = parts.includes("alt") || parts.includes("option");
			const hasCmd = parts.includes("cmd") || parts.includes("command");
			const hasCtrl = parts.includes("ctrl") || parts.includes("control");
			const hasShift = parts.includes("shift");
			const key = parts[parts.length - 1];

			// 检查按键组合
			if (
				event.altKey === hasAlt &&
				event.metaKey === hasCmd &&
				event.ctrlKey === hasCtrl &&
				event.shiftKey === hasShift &&
				event.key.toLowerCase() === key
			) {
				event.preventDefault();
				safeHandler();
			}
		};

		// 只在应用获得焦点时监听
		let isWindowFocused = true; // 默认认为窗口有焦点

		const safeKeyHandler = (event: KeyboardEvent) => {
			if (isWindowFocused) {
				try {
					handleKeyDown(event);
				} catch (err) {
					console.error("应用内快捷键处理出错:", err);
				}
			}
		};

		document.addEventListener("keydown", safeKeyHandler);
		
		// 监听窗口焦点变化
		const handleFocus = () => { isWindowFocused = true; };
		const handleBlur = () => { isWindowFocused = false; };
		
		window.addEventListener("focus", handleFocus);
		window.addEventListener("blur", handleBlur);

		setMethod('fallback');
		setIsActive(true);
		console.log(`应用内快捷键启用: ${shortcut}`);

		return () => {
			document.removeEventListener("keydown", safeKeyHandler);
			window.removeEventListener("focus", handleFocus);
			window.removeEventListener("blur", handleBlur);
		};
	}, [shortcut, safeHandler]);

	// 主要的快捷键注册逻辑
	useEffect(() => {
		if (!shortcut) {
			setMethod('disabled');
			setIsActive(false);
			return;
		}

		let cleanup: (() => void) | undefined;

		const setupShortcut = async () => {
			// 首先尝试全局快捷键
			const globalSuccess = await tryGlobalShortcut();
			
			if (!globalSuccess) {
				// 如果全局快捷键失败，使用回退方案
				cleanup = useFallbackShortcut();
			}
		};

		setupShortcut();

		return () => {
			if (cleanup) {
				cleanup();
			}
			
			// 清理全局快捷键
			if (method === 'global' && shortcut) {
				import("@tauri-apps/plugin-global-shortcut")
					.then(({ unregister }) => unregister(shortcut))
					.catch(() => {}); // 忽略清理错误
			}
		};
	}, [shortcut, tryGlobalShortcut, useFallbackShortcut, method]);

	return {
		isActive,
		error,
		method,
		shortcut,
	};
};
