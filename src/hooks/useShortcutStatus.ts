import { useState, useEffect } from 'react';

interface ShortcutStatus {
  globalShortcutAvailable: boolean;
  accessibilityPermission: boolean;
  recommendedMode: 'global' | 'fallback' | 'disabled';
  error?: string;
}

/**
 * 检查快捷键功能的可用性
 */
export const useShortcutStatus = () => {
  const [status, setStatus] = useState<ShortcutStatus>({
    globalShortcutAvailable: false,
    accessibilityPermission: false,
    recommendedMode: 'disabled',
  });

  useEffect(() => {
    const checkShortcutStatus = async () => {
      try {
        // 检查是否在 Tauri 环境中
        if (typeof window === 'undefined' || !window.__TAURI__) {
          setStatus({
            globalShortcutAvailable: false,
            accessibilityPermission: false,
            recommendedMode: 'fallback',
            error: '不在 Tauri 环境中',
          });
          return;
        }

        let globalShortcutAvailable = false;
        let accessibilityPermission = false;
        let error: string | undefined;

        // 检查全局快捷键插件是否可用
        try {
          const { isRegistered } = await import('@tauri-apps/plugin-global-shortcut');
          // 尝试检查一个不存在的快捷键，如果不报错说明插件可用
          await isRegistered('test-shortcut-that-does-not-exist');
          globalShortcutAvailable = true;
        } catch (err) {
          console.warn('全局快捷键插件不可用:', err);
          error = err instanceof Error ? err.message : String(err);
        }

        // 检查辅助功能权限（仅在 macOS 上）
        if (globalShortcutAvailable) {
          try {
            // 检查是否在 macOS 上
            const { platform } = await import('@tauri-apps/plugin-os');
            const currentPlatform = await platform();
            
            if (currentPlatform === 'macos') {
              // 尝试检查辅助功能权限
              try {
                const { checkAccessibilityPermission } = await import('tauri-plugin-macos-permissions-api');
                accessibilityPermission = await checkAccessibilityPermission();
              } catch (permErr) {
                console.warn('无法检查辅助功能权限:', permErr);
                // 假设没有权限，但不影响功能
                accessibilityPermission = false;
              }
            } else {
              // 非 macOS 系统，假设有权限
              accessibilityPermission = true;
            }
          } catch (osErr) {
            console.warn('无法检查操作系统:', osErr);
            accessibilityPermission = false;
          }
        }

        // 确定推荐模式
        let recommendedMode: 'global' | 'fallback' | 'disabled' = 'disabled';
        
        if (globalShortcutAvailable && accessibilityPermission) {
          recommendedMode = 'global';
        } else if (globalShortcutAvailable || typeof document !== 'undefined') {
          recommendedMode = 'fallback';
        }

        setStatus({
          globalShortcutAvailable,
          accessibilityPermission,
          recommendedMode,
          error,
        });

        console.log('快捷键状态检查完成:', {
          globalShortcutAvailable,
          accessibilityPermission,
          recommendedMode,
          error,
        });

      } catch (err) {
        const errorMsg = err instanceof Error ? err.message : String(err);
        console.error('快捷键状态检查失败:', errorMsg);
        
        setStatus({
          globalShortcutAvailable: false,
          accessibilityPermission: false,
          recommendedMode: 'fallback', // 出错时使用回退模式
          error: errorMsg,
        });
      }
    };

    checkShortcutStatus();
  }, []);

  return status;
};
