import React, { Component, ReactNode } from 'react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
}

/**
 * 快捷键错误边界组件
 * 捕获快捷键相关的错误，防止整个应用崩溃
 */
export class ShortcutErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    // 检查是否是快捷键相关的错误
    const isShortcutError = 
      error.message.includes('shortcut') ||
      error.message.includes('global-shortcut') ||
      error.message.includes('accessibility') ||
      error.message.includes('permission') ||
      error.stack?.includes('useRegister') ||
      error.stack?.includes('useShortcut') ||
      error.stack?.includes('useSafeShortcut');

    if (isShortcutError) {
      console.warn('快捷键错误被捕获:', error);
      return { hasError: true, error };
    }

    // 如果不是快捷键相关错误，重新抛出
    throw error;
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('ShortcutErrorBoundary 捕获到错误:', error, errorInfo);
    
    // 可以在这里上报错误到日志服务
    // reportError(error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      // 快捷键错误不影响应用的正常使用，只是记录日志
      console.warn('快捷键功能暂时不可用，应用继续正常运行');
      
      // 返回原始内容，不显示错误界面
      return this.props.children;
    }

    return this.props.children;
  }
}

export default ShortcutErrorBoundary;
