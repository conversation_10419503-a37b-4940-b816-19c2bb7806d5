import type { WindowLabel } from "@/types/plugin";
import { invoke } from "@tauri-apps/api/core";
import { emit } from "@tauri-apps/api/event";
import { getCurrentWebviewWindow } from "@tauri-apps/api/webviewWindow";
import {
	LogicalPosition,
	LogicalSize,
	currentMonitor,
} from "@tauri-apps/api/window";

const COMMAND = {
	SHOW_WINDOW: "plugin:onepaste-window|show_window",
	HIDE_WINDOW: "plugin:onepaste-window|hide_window",
	SHOW_TASKBAR_ICON: "plugin:onepaste-window|show_taskbar_icon",
};

/**
 * 显示窗口
 */
export const showWindow = (label?: WindowLabel) => {
	console.log(`显示窗口: ${label || 'current'}`);
	if (label) {
		emit(LISTEN_KEY.SHOW_WINDOW, label);
	} else {
		invoke(COMMAND.SHOW_WINDOW);
	}
};

/**
 * 隐藏窗口
 */
export const hideWindow = () => {
	console.log("隐藏窗口");
	invoke(COMMAND.HIDE_WINDOW);
};

/**
 * 切换窗口的显示和隐藏
 */
export const toggleWindowVisible = async () => {
	const appWindow = getCurrentWebviewWindow();

	console.log("开始切换窗口显示状态");

	try {
		let focused = await appWindow.isFocused();
		const visible = await appWindow.isVisible();

		console.log(`窗口状态: focused=${focused}, visible=${visible}`);

		if (isLinux) {
			focused = await appWindow.isVisible();
		}

		if (focused || visible) {
			console.log("窗口可见或有焦点，隐藏窗口");
			hideWindow();
		} else {
			console.log("窗口不可见，显示窗口");
			if (appWindow.label === WINDOW_LABEL.MAIN) {
				const { window } = clipboardStore;

				// 激活时回到顶部
				if (window.backTop) {
					await emit(LISTEN_KEY.ACTIVATE_BACK_TOP);
				}

				if (window.style === "float" && window.position !== "remember") {
					const current = await currentMonitor();
					const monitor = await getCursorMonitor();

					if (current && monitor) {
						let { position, size, cursorX, cursorY } = monitor;
						const windowSize = await appWindow.innerSize();
						const { width, height } = windowSize.toLogical(current.scaleFactor);

						if (window.position === "follow") {
							cursorX = Math.min(cursorX, position.x + size.width - width);
							cursorY = Math.min(cursorY, position.y + size.height - height);
						} else {
							cursorX = position.x + (size.width - width) / 2;
							cursorY = position.y + (size.height - height) / 2;
						}

						await appWindow.setPosition(
							new LogicalPosition(Math.round(cursorX), Math.round(cursorY)),
						);
					}
				} else if (window.style === "dock") {
					const monitor = await getCursorMonitor();

					if (monitor) {
						const { width, height } = monitor.size;
						const windowHeight = 400;
						const { x } = monitor.position;
						const y = height - windowHeight;

						await appWindow.setSize(new LogicalSize(width, windowHeight));
						await appWindow.setPosition(new LogicalPosition(x, y));
					}
				}
			}

			showWindow();
		}
	} catch (error) {
		console.error("切换窗口状态时出错:", error);
	}
};

/**
 * 显示任务栏图标
 */
export const showTaskbarIcon = (visible = true) => {
	invoke(COMMAND.SHOW_TASKBAR_ICON, { visible });
};
